'use client'

import { motion } from 'framer-motion';
import { Button } from '@/common/components/atoms';
import lang from '@/common/lang';

interface PricingSectionProps {
  handleGetStarted: () => void;
}

export const PricingSection = ({ handleGetStarted }: PricingSectionProps) => {
  const { clientPage } = lang;

  return (
    <motion.section
      initial={{
        opacity: 0,
        y: 20,
      }}
      animate={{
        opacity: 1,
        y: 0,
      }}
      transition={{
        duration: 0.5,
        delay: 0.7,
      }}
      className="mb-24"
      id="pricing"
    >
      <div className="text-center mb-12">
        <h2 className="text-2xl md:text-4xl font-bold text-white mb-4">
          {clientPage.pricing.title}
        </h2>
        <p className="text-lg text-gray-300 max-w-2xl mx-auto">
          {clientPage.pricing.description}
        </p>
      </div>

      <div className="flex justify-center mb-10">
        <div className="bg-violets-are-blue/5 border border-white/10 rounded-full p-1 flex">
          <button className="px-6 py-2 rounded-full bg-gradient-to-tr from-han-purple to-tulip text-white font-medium text-sm">
            {clientPage.pricing.billingOptions.monthly}
          </button>
          <button className="px-6 py-2 rounded-full text-gray-300 font-medium text-sm hover:text-white transition-colors">
            {clientPage.pricing.billingOptions.yearly}
          </button>
        </div>
        <div className="ml-3 bg-violets-are-blue/10 text-white text-xs font-medium px-2 py-1 rounded-full flex items-center">
          {clientPage.pricing.billingOptions.discount}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8 max-w-4xl mx-auto">
        {clientPage.pricing.plans.map((plan, index) => (
          <div
            key={index}
            className={`${plan.popular
              ? "bg-gradient-to-b from-han-purple/20 to-tulip/10 border border-white/10 hover:border-white/20"
              : "bg-violets-are-blue/5 border border-white/5 hover:border-violets-are-blue/20"}
              rounded-3xl p-6 md:p-8 transition-all duration-300 relative`}
          >
            {plan.popular && (
              <div className="absolute -top-3 right-8 bg-gradient-to-r from-han-purple to-tulip text-white text-xs font-bold px-3 py-1 rounded-full">
                {plan.popular}
              </div>
            )}

            <div className="mb-6">
              <h3 className="text-xl font-bold text-white mb-2">{plan.name}</h3>
              <div className="flex items-end mb-4">
                <span className="text-3xl font-bold text-white">{plan.price}</span>
                <span className="text-gray-400 ml-1">{plan.period}</span>
              </div>
              <p className="text-gray-300">{plan.description}</p>
            </div>

            <Button
              size="lg"
              variant="gradient"
              onClick={handleGetStarted}
              className="w-full mb-6"
            >
              {plan.buttonText}
            </Button>

            <div className={`border-t ${plan.popular ? "border-white/10" : "border-white/5"} pt-6`}>
              <ul className="space-y-3">
                {plan.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-start">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-tulip mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span className="text-gray-300">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        ))}
      </div>
    </motion.section>
  );
};
